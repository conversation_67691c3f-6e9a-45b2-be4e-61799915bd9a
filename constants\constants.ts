export const geneders: { label: string; value: string }[] = [
  {
    label: "Male",
    value: "m",
  },
  {
    label: "Female",
    value: "f",
  },
  {
    label: "Trans Male",
    value: "tm",
  },
  {
    label: "Trans Female",
    value: "tf",
  },
  {
    label: "Non Binary",
    value: "nb",
  },
  {
    label: "Other",
    value: "o",
  },
];

export const sexualOrientations: { label: string; value: string }[] = [
  {
    label: "Gay",
    value: "g",
  },
  {
    label: "Lesbian",
    value: "l",
  },
  {
    label: "Bisexual",
    value: "b",
  },
  {
    label: "Gay Friendly Straight",
    value: "gfs",
  },
];

export const smokeCigarettes: { label: string; value: string }[] = [
  {
    label: "Yes",
    value: "y",
  },
  {
    label: "No",
    value: "n",
  },
  {
    label: "Only Outside",
    value: "oo",
  },
];

export const smokeMarijuana: { label: string; value: string }[] = [
  {
    label: "Yes",
    value: "y",
  },
  {
    label: "No",
    value: "n",
  },
  {
    label: "Only Outside",
    value: "o",
  },
  {
    label: "Prefers Not to Say",
    value: "pns",
  },
];

export const workFromHome: { label: string; value: string }[] = [
  {
    label: "Yes",
    value: "y",
  },
  {
    label: "No",
    value: "n",
  },
  {
    label: "Sometimes",
    value: "s",
  },
];

export const travels: { label: string; value: string }[] = [
  {
    label: "Frequently",
    value: "f",
  },
  {
    label: "Occasionally",
    value: "o",
  },
  {
    label: "Rarely",
    value: "r",
  },
  {
    label: "Never",
    value: "n",
  },
];

export const cleanliness: { label: string; value: string }[] = [
  {
    label: "Very Important",
    value: "vi",
  },
  {
    label: "Important",
    value: "i",
  },
  {
    label: "Somewhat Important",
    value: "si",
  },
  {
    label: "Not too important",
    value: "nti",
  },
];

export const describeMyselfAs: { label: string; value: string }[] = [
  {
    label: "Fitness Junkie",
    value: "fj",
  },
  {
    label: "Night Owl",
    value: "no",
  },
  {
    label: "Early Bird",
    value: "eb",
  },
  {
    label: "Foodie",
    value: "f",
  },
  {
    label: "Bookworm",
    value: "bw",
  },
  {
    label: "Party Boy/Girl",
    value: "pbg",
  },
];

export const zodiacs: { label: string; value: string }[] = [
  {
    label: "Aries",
    value: "a",
  },
  {
    label: "Taurus",
    value: "t",
  },
  {
    label: "Gemini",
    value: "g",
  },
  {
    label: "Cancer",
    value: "c",
  },
  {
    label: "Leo",
    value: "l",
  },
  {
    label: "Virgo",
    value: "v",
  },
  {
    label: "Libra",
    value: "li",
  },
  {
    label: "Scorpio",
    value: "s",
  },
  {
    label: "Sagittarius",
    value: "sa",
  },
  {
    label: "Capricorn",
    value: "ca",
  },
  {
    label: "Aquarius",
    value: "aq",
  },
  {
    label: "Pisces",
    value: "p",
  },
];

export const residenceTypes: { label: string; value: string }[] = [
  {
    label: "House",
    value: "house",
  },
  {
    label: "Apartment",
    value: "apartment",
  },
  {
    label: "Condo",
    value: "condo",
  },
  {
    label: "Townhouse",
    value: "townhouse",
  },
];

export const bedRooms: { label: string; value: string }[] = [
  {
    label: "Studio",
    value: "studio",
  },
  {
    label: "1 Bedroom",
    value: "1",
  },
  {
    label: "2 Bedrooms",
    value: "2",
  },
  {
    label: "3 Bedrooms",
    value: "3",
  },
  {
    label: "4+ Bedrooms",
    value: "4+",
  },
];

export const bathRooms: { label: string; value: string }[] = [
  {
    label: "1 Bathroom",
    value: "1",
  },
  {
    label: "2 Bathrooms",
    value: "2",
  },
  {
    label: "3 Bathrooms",
    value: "3",
  },
  {
    label: "4+ Bathrooms",
    value: "4+",
  },
];

export const ownerOccupied: { label: string; value: string }[] = [
  {
    label: "Yes",
    value: "yes",
  },
  {
    label: "No",
    value: "no",
  },
];

export const numberOfOccupants: { label: string; value: string }[] = [
  {
    label: "1 Occupant",
    value: "1",
  },
  {
    label: "2 Occupants",
    value: "2",
  },
  {
    label: "3 Occupants",
    value: "3",
  },
  {
    label: "4+ Occupants",
    value: "4+",
  },
];

export const amenities: { label: string; value: string }[] = [
  { label: "Wifi", value: "wifi" },
  { label: "Cable/Satellite", value: "cable_satellite" },
  { label: "Alarm System", value: "alarm_system" },
  { label: "Air Conditioning", value: "air_conditioning" },
  { label: "Heating", value: "heating" },
  { label: "Central Air", value: "central_air" },
  { label: "Laundry On-Site", value: "laundry_on_site" },
  { label: "Laundry In-Unit", value: "laundry_in_unit" },
  { label: "Clothes Dryer", value: "clothes_dryer" },
  { label: "Dishwasher", value: "dishwasher" },
  { label: "Fireplace", value: "fireplace" },
  { label: "Balcony/Patio", value: "balcony_patio" },
  { label: "Elevator", value: "elevator" },
  { label: "Storage", value: "storage" },
  { label: "Doorman", value: "doorman" },
  { label: "Pool", value: "pool" },
  { label: "Tennis Court", value: "tennis_court" },
  { label: "Gym", value: "gym" },
  { label: "Hot tub/Jacuzzi", value: "hot_tub_jacuzzi" },
  { label: "BBQ Grill", value: "bbq_grill" },
  { label: "Yard", value: "yard" },
  { label: "Wheelchair accessible", value: "wheelchair_accessible" },
  { label: "Maid/Cleaning Service", value: "maid_cleaning_service" },
];

export const parkingOptions: { label: string; value: string }[] = [
  { label: "Assigned Covered", value: "assigned_covered" },
  { label: "Assigned Uncovered", value: "assigned_uncovered" },
  { label: "On-street (no permit)", value: "on_street_no_permit" },
  { label: "No parking Available", value: "no_parking_available" },
];

export const neighborhood: { label: string; value: string }[] = [
  { label: "Gym", value: "gym" },
  { label: "Beach", value: "beach" },
  { label: "Nightlife", value: "nightlife" },
  { label: "Parks", value: "parks" },
  { label: "Dog Parks", value: "dog_parks" },
  { label: "Airport", value: "airport" },
  { label: "Freeway access", value: "freeway_access" },
  { label: "Restaurants", value: "restaurants" },
  { label: "Cafes/coffee shop", value: "cafes_coffee_shop" },
  { label: "Shopping", value: "shopping" },
  { label: "Movie Theaters", value: "movie_theaters" },
  { label: "College/University", value: "college_university" },
  { label: "Public Transport", value: "public_transport" },
];

export const currentPets: { label: string; value: string }[] = [
  { label: "No Pets", value: "no_pets" },
  { label: "Dogs", value: "dogs" },
  { label: "Cats", value: "cats" },
  { label: "Caged pet", value: "caged_pet" },
  { label: "Any Pet", value: "any_pet" },
];

export const allowPets: { label: string; value: string }[] = [
  { label: "No Pets", value: "no_pets" },
  { label: "Dogs", value: "dogs" },
  { label: "Cats", value: "cats" },
  { label: "Caged pet", value: "caged_pet" },
  { label: "Any Pet", value: "any_pet" },
];

export const allowedGuests: { label: string; value: string }[] = [
  { label: "Anytime", value: "anytime" },
  { label: "Occasionally", value: "occasionally" },
  { label: "Weekends only", value: "weekends_only" },
  { label: "Never", value: "never" },
];
