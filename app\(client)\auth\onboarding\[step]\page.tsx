"use client";

import React, { useEffect } from "react";
import { useSignupStore } from "@/store/userSignUpStore";
import { useParams, useRouter } from "next/navigation";
import {
  ABOUT_YOU,
  RENTAL_BASICS,
  RENTAL_PREFERENCES,
  ROOMMATE_PREFERENCES,
  TENANT_PREFERENCES,
  THE_HOME,
  THE_ROOM,
  THE_SPACE,
  UPLOAD_PROFILE_PHOTO,
  UPLOAD_SPACE_PHOTOS,
} from "@/constants/auth.constants";
import AboutYouStep from "./_components/AboutYouStep";
import ProfilePhotoStep from "./_components/ProfilePhotoStep";
import HomeStep from "./_components/HomeStep";
import RoomStep from "./_components/RoomStep";
import SpaceStep from "./_components/SpaceStep";
import SpacePhotosStep from "./_components/SpacePhotosStep";
import RoommatePreferencesStep from "./_components/RoommatePreferencesStep";
import RentalBasicsStep from "./_components/RentalBasicsStep";
import TenantPreferencesStep from "./_components/TenantPreferencesStep";
import RentalPreferencesStep from "./_components/RentalPreferencesStep";

const OnboardingPage = () => {
  const { step } = useParams();
  const router = useRouter();
  const currentStep = parseInt(step as string);
  const { data, steps, setCurrentStep, currentStep: storeCurrentStep } = useSignupStore();

  useEffect(() => {
    if (!steps.length) {
      router.push("/auth/register");
    } else {
      setCurrentStep(currentStep);
    }
  }, [currentStep]);

  const stepName = steps[currentStep];

  console.log(data);
  console.log(storeCurrentStep);

  // Map step names to components
  const renderStepForm = () => {
    switch (stepName) {
      case ABOUT_YOU:
        return <AboutYouStep />;
      case UPLOAD_PROFILE_PHOTO:
        return <ProfilePhotoStep />;
      case THE_HOME:
        return <HomeStep />;
      case THE_ROOM:
        return <RoomStep />;
      case THE_SPACE:
        return <SpaceStep />;
      case UPLOAD_SPACE_PHOTOS:
        return <SpacePhotosStep />;
      case ROOMMATE_PREFERENCES:
        return <RoommatePreferencesStep />;
      case RENTAL_BASICS:
        return <RentalBasicsStep />;
      case TENANT_PREFERENCES:
        return <TenantPreferencesStep />;
      case RENTAL_PREFERENCES:
        return <RentalPreferencesStep />;
      default:
        return <div>Form not found for step: {stepName}</div>;
    }
  };

  return (
    <div>
      <h2 className="text-2xl font-semibold text-secondary">{stepName}</h2>
      {renderStepForm()}
    </div>
  );
};

export default OnboardingPage;
