import { create } from "zustand";

interface SignupData {
  firstName: string;
  lastName: string;
  email: string;
  intent: string;
  spaceType: string;
  genderIdentity: string;
  sexualOrientation: string;
  age: number;
  smokeCigarettes: string;
  smokeMarijuana: string;
  workFromHome: string;
  travel: string;
  cleanliness: string;
  describeMyselfAs: string;
  zodiac: string;
  selfDescription: string;
  fluentLanguages: string[];
  avatar: string;
}

interface SignupStore {
  data: SignupData;
  setData: (values: Partial<SignupData>) => void;
  clear: () => void;

  steps: string[];
  setSteps: (steps: string[]) => void;

  currentStep: number;
  setCurrentStep: (step: number) => void;
}

export const useSignupStore = create<SignupStore>((set) => ({
  data: {
    firstName: "",
    lastName: "",
    email: "",
    intent: "",
    spaceType: "",
    genderIdentity: "",
    sexualOrientation: "",
    age: 0,
    smokeCigarettes: "",
    smokeMarijuana: "",
    workFromHome: "",
    travel: "",
    cleanliness: "",
    describeMyselfAs: "",
    zodiac: "",
    selfDescription: "",
    fluentLanguages: [],
    avatar: "",
  },
  setData: (values) => set((state) => ({ data: { ...state.data, ...values } })),
  clear: () =>
    set({
      data: {
        firstName: "",
        lastName: "",
        email: "",
        intent: "",
        spaceType: "",
        genderIdentity: "",
        sexualOrientation: "",
        age: 0,
        smokeCigarettes: "",
        smokeMarijuana: "",
        workFromHome: "",
        travel: "",
        cleanliness: "",
        describeMyselfAs: "",
        zodiac: "",
        selfDescription: "",
        fluentLanguages: [],
        avatar: "",
      },
    }),

  steps: [],
  setSteps: (steps) => set({ steps }),

  currentStep: 0,
  setCurrentStep: (step) => set({ currentStep: step }),
}));
