"use client";

import React from "react";
import FileUpload from "@/components/ui/file-upload";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useSignupStore } from "@/store/userSignUpStore";

const ProfilePhotoStep = () => {
  const router = useRouter();
  const { currentStep, steps, setData } = useSignupStore();

  const handlePrevious = () => {
    if (currentStep === 0) {
      router.push("/auth/register");
    }
    router.push(`/auth/onboarding/${currentStep - 1}`);
  };

  const handleNext = () => {
    if (currentStep === steps.length - 1) return;
    router.push(`/auth/onboarding/${currentStep + 1}`);
  };

  const handleFileChange = (file: File) => {
    const preview = URL.createObjectURL(file);
    console.log("Preview link:", preview);
    setData({ avatar: preview });
  };

  return (
    <div>
      <FileUpload onFileChange={handleFileChange} />
      <div className="flex justify-between mt-8">
        <Button type="button" variant="outline" onClick={handlePrevious}>
          Previous
        </Button>
        <Button type="submit" onClick={handleNext} disabled={currentStep === steps.length - 1}>
          {currentStep === steps.length - 1 ? "Complete" : "Next"}
        </Button>
      </div>
    </div>
  );
};

export default ProfilePhotoStep;
