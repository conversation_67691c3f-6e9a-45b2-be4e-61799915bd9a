"use client";

import React from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useSignupStore } from "@/store/userSignUpStore";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  allowedGuests,
  bathRooms,
  bedRooms,
  numberOfOccupants,
  ownerOccupied,
  residenceTypes,
  smokeCigarettes,
  smokeMarijuana,
} from "@/constants/constants";
import { Textarea } from "@/components/ui/textarea";

const HomeStep = () => {
  const router = useRouter();
  const { currentStep, steps } = useSignupStore();
  const form = useForm({
    defaultValues: {
      fullAddress: "",
      residenceType: "",
      size: 0,
      bedrooms: 0,
      bathrooms: 0,
      ownerOccupied: "",
      numPeopleInHome: "",
      amenities: [],
      parking: [],
      neighborhood: [],
      currentPets: [],
      allowedPets: [],
      smokeCigarettes: "",
      smokeMarijuana: "",
      overnightGuestsAllowed: "",
      homeDescription: "",
    },
  });

  function onSubmit(values: any) {
    console.log("Home form data:", values);
  }

  const handlePrevious = () => {
    if (currentStep === 0) {
      router.push("/auth/register");
    }
    router.push(`/auth/onboarding/${currentStep - 1}`);
  };

  const handleNext = () => {
    if (currentStep === steps.length - 1) return;
    router.push(`/auth/onboarding/${currentStep + 1}`);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          name="fullAddress"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Full Address</FormLabel>
              <FormControl>
                <Input placeholder="Full Address" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="residenceType"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Residence Type</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a residence type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {residenceTypes.map((residence, idx) => (
                    <SelectItem key={idx} value={residence.value}>
                      {residence.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="size"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Size (in square feet)</FormLabel>
              <FormControl>
                <Input placeholder="Size" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="bedrooms"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Number of Bedrooms</FormLabel>
              <Select onValueChange={field.onChange}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a residence type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {bedRooms.map((bedRoom, idx) => (
                    <SelectItem key={idx} value={bedRoom.value}>
                      {bedRoom.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="bathrooms"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Number of Bathrooms</FormLabel>
              <Select onValueChange={field.onChange}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a residence type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {bathRooms.map((bathRoom, idx) => (
                    <SelectItem key={idx} value={bathRoom.value}>
                      {bathRoom.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="ownerOccupied"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Owner Occupied</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a residence type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {ownerOccupied.map((owner, idx) => (
                    <SelectItem key={idx} value={owner.value}>
                      {owner.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="numPeopleInHome"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Number of People in the Home (including new roommate)?</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select number of people in the home" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {numberOfOccupants.map((occupant, idx) => (
                    <SelectItem key={idx} value={occupant.value}>
                      {occupant.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="smokeCigarettes"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Smoke Cigarettes</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {smokeCigarettes.map((smoke, idx) => (
                    <SelectItem key={idx} value={smoke.value}>
                      {smoke.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="smokeMarijuana"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Smoke Marijuana</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {smokeMarijuana.map((smoke, idx) => (
                    <SelectItem key={idx} value={smoke.value}>
                      {smoke.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="overnightGuestsAllowed"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Overnight Guests Allowed</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {allowedGuests.map((guest, idx) => (
                    <SelectItem key={idx} value={guest.value}>
                      {guest.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="homeDescription"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Home Description</FormLabel>
              <FormControl>
                <Textarea placeholder="Home Description" {...field} className="h-40" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-between mt-8">
          <Button type="button" variant="outline" onClick={handlePrevious}>
            Previous
          </Button>
          <Button type="submit" onClick={handleNext} disabled={currentStep === steps.length - 1}>
            {currentStep === steps.length - 1 ? "Complete" : "Next"}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default HomeStep;
