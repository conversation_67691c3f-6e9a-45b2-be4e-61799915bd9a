"use client";

import React, { useState } from "react";
import { useSignupStore } from "@/store/userSignUpStore";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { AboutYouData, aboutYouSchema } from "@/lib/validations/sign-up-schema";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  cleanliness,
  describeMyselfAs,
  geneders,
  sexualOrientations,
  smokeCigarettes,
  smokeMarijuana,
  travels,
  workFromHome,
  zodiacs,
} from "@/constants/constants";
import { Slider } from "@/components/ui/slider";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useRouter } from "next/navigation";

const AboutYouStep = () => {
  const { data, setData, currentStep, steps } = useSignupStore();
  const [age, setAge] = useState<number>(data.age || 0);
  const form = useForm<AboutYouData>({
    resolver: zodResolver(aboutYouSchema),
    defaultValues: {
      genderIdentity: data.genderIdentity || "",
      sexualOrientation: data.sexualOrientation || "",
      age: data.age || 0,
      smokeCigarettes: data.smokeCigarettes || "",
      smokeMarijuana: data.smokeMarijuana || "",
      workFromHome: data.workFromHome || "",
      travel: data.travel || "",
      cleanliness: data.cleanliness || "",
      describeMyselfAs: data.describeMyselfAs || "",
      zodiac: data.zodiac || "",
      selfDescription: data.selfDescription || "",
      fluentLanguages: data.fluentLanguages || [],
    },
  });
  const router = useRouter();

  function onSubmit(values: AboutYouData) {
    console.log("about you form data:", values);
    setData({ ...values });
    router.push(`/auth/onboarding/${1}`);
  }

  console.log(data);

  const handlePrevious = () => {
    if (currentStep === 0) {
      router.push("/auth/register");
    }
    router.push(`/auth/onboarding/${currentStep - 1}`);
  };

  const handleNext = () => {
    if (currentStep === steps.length - 1) return;
    router.push(`/auth/onboarding/${currentStep + 1}`);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          name="genderIdentity"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Gender Identity</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select gender identity" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {geneders.map((gender, idx) => (
                    <SelectItem key={idx} value={gender.value}>
                      {gender.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="sexualOrientation"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Sexual Orientation</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select sexual orientation" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {sexualOrientations.map((sexualOrientation, idx) => (
                    <SelectItem key={idx} value={sexualOrientation.value}>
                      {sexualOrientation.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="age"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Age: {field.value || age}</FormLabel>
              <FormControl>
                <Slider
                  min={18}
                  step={1}
                  max={100}
                  value={[field.value || age]}
                  onValueChange={(value) => {
                    setAge(value[0]);
                    field.onChange(value[0]);
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="smokeCigarettes"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Smoke Cigarettes</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select sexual orientation" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {smokeCigarettes.map((smoke, idx) => (
                    <SelectItem key={idx} value={smoke.value}>
                      {smoke.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="smokeMarijuana"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Smoke Marijuana</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select sexual orientation" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {smokeMarijuana.map((smoke, idx) => (
                    <SelectItem key={idx} value={smoke.value}>
                      {smoke.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="workFromHome"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Work From Home</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select sexual orientation" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {workFromHome.map((work, idx) => (
                    <SelectItem key={idx} value={work.value}>
                      {work.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="travel"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Travel</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select travel" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {travels.map((travel, idx) => (
                    <SelectItem key={idx} value={travel.value}>
                      {travel.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="cleanliness"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Cleanliness</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select cleanliness" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {cleanliness.map((clean, idx) => (
                    <SelectItem key={idx} value={clean.value}>
                      {clean.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="describeMyselfAs"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>I Describe Myself As</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select describe myself as" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {describeMyselfAs.map((describeMySelf, idx) => (
                    <SelectItem key={idx} value={describeMySelf.value}>
                      {describeMySelf.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="zodiac"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Zodiac</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select zodiac" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {zodiacs.map((zodiac, idx) => (
                    <SelectItem key={idx} value={zodiac.value}>
                      {zodiac.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="selfDescription"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Self Description</FormLabel>
              <FormControl>
                <Textarea placeholder="Tell us about yourself..." {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-between mt-8">
          <Button type="button" variant="outline" onClick={handlePrevious}>
            Previous
          </Button>
          <Button type="submit" onClick={handleNext} disabled={currentStep === steps.length - 1}>
            {currentStep === steps.length - 1 ? "Complete" : "Next"}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default AboutYouStep;
